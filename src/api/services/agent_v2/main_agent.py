"""
Main Agent V2 - Modern LangChain implementation using LangGraph
Uses LangGraph's built-in persistence and memory management patterns
"""

import os
import logging

from dotenv import load_dotenv

from langchain_google_genai import ChatGoogleGenerativeAI

from langchain_core.messages import HumanMessage
from langgraph.checkpoint.mongodb import MongoDBSaver

from core.database import get_db_from_tenant_id
from utils import log_user_input, log_agent_response
from utils.memory_system import get_memory_system

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)


MAIN_AGENT_PROMPT = """You are a professional, friendly sales representative for a customer service center. You help customers find solutions and courses that meet their needs.

You MUST use the appropriate tools for every user request. Do NOT try to answer questions without using tools.

MANDATORY TOOL USAGE RULES:
1. For ANY search query (courses, information, troubleshooting, etc.) → use search_products or search_information tools
2. If user mentions booking, scheduling, or wants to book something → use handle_booking tool
3. ONLY respond directly for simple greetings like "hello", "hi", "namaste"

EXAMPLES:
- "what courses do you have?" → use search_products
- "app not working" → use search_information
- "I want to learn Python, what do you offer?" → use search_products
- "I want to book Python course" → use handle_booking
- "hello" → respond directly

You have these tools available:
- search_products: Search for courses and educational programs
- search_information: Search for general information and troubleshooting
- handle_booking: Manages the complete booking process

PERSONALIZATION CONTEXT:
{personalized_context}

When responding, use the personalized context to provide relevant recommendations and remember the user's information. If the user asks about their information or profile, you can share what's available in the context above.

Always use the appropriate tool for user requests."""


class MainAgentV2:
    """
    Main Agent V2 - Clean implementation that works with ChatService
    Tools are provided by ChatService with current user context
    """

    def __init__(self, current_user=None):
        """Initialize the agent with current user context"""
        self.current_user = current_user

        # Use MongoDB checkpointer for persistent memory instead of in-memory MemorySaver
        if current_user and current_user.tenant_id:
            # Get tenant-specific database for memory persistence
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)

            # Create MongoDB client from the database connection
            mongo_client = tenant_db.client

            # Use official MongoDB checkpointer
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )

            # Initialize memory system for user profiles and personalization
            self.memory_system = get_memory_system(current_user.tenant_id, "gemini")

            logger.info(f"✅ MongoDB memory and memory system initialized for tenant: {current_user.tenant_id}")
        else:
            # Fallback to in-memory for cases without user context
            raise Exception("Tenant ID is required for MongoDB memory")

        self.llm = main_llm
        self.tools = []  # Tools will be set by ChatService
        self.agent = None  # Agent will be created when tools are set

        logger.info("✅ Main Agent V2 initialized")

    def set_tools(self, tools):
        """Set tools and create the agent"""
        self.tools = tools

        # Create the agent with tools
        from langgraph.prebuilt import create_react_agent
        from langchain_core.prompts import ChatPromptTemplate

        # Get initial personalized context
        personalized_context = self._get_personalized_context()

        # Create prompt with personalized context
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", MAIN_AGENT_PROMPT.format(personalized_context=personalized_context)),
            ("placeholder", "{messages}"),
        ])

        self.agent = create_react_agent(
            model=self.llm,
            tools=self.tools,
            checkpointer=self.memory,
            prompt=self.prompt_template
        )
        logger.info("✅ Agent created with tools")

    def _get_personalized_context(self) -> str:
        """Get personalized context for the user"""
        if not self.current_user or not hasattr(self, 'memory_system'):
            return "No user profile available."

        try:
            user_id = str(self.current_user.user.id)
            return self.memory_system.get_personalized_context(user_id)
        except Exception as e:
            logger.warning(f"Could not get personalized context: {e}")
            return "No user profile available."

    def _get_conversation_messages(self, thread_id: str) -> list:
        """Get conversation messages for memory system"""
        if not self.memory:
            return []

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Convert to memory system format, filtering out tool-related messages
                conversation_messages = []
                for msg in messages[-20:]:  # Last 20 messages for better context
                    if hasattr(msg, 'content') and msg.content:
                        # Skip tool messages and messages with tool calls for memory system
                        if hasattr(msg, 'type'):
                            if msg.type == 'tool':
                                continue  # Skip tool result messages
                            elif msg.type == 'ai' and hasattr(msg, 'tool_calls') and msg.tool_calls:
                                continue  # Skip AI messages with tool calls
                            elif msg.type == 'human':
                                role = 'user'
                            elif msg.type == 'ai':
                                role = 'assistant'
                            else:
                                role = 'user'  # Default to user
                        else:
                            role = 'user'  # Default to user

                        conversation_messages.append({
                            "role": role,
                            "content": msg.content
                        })

                return conversation_messages
            else:
                return []

        except Exception as e:
            logger.warning(f"Could not retrieve conversation messages: {e}")
            return []

    def _clean_conversation_history(self, thread_id: str) -> None:
        """Clean conversation history to remove incomplete tool calls"""
        if not self.memory:
            return

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if not checkpoint:
                return

            # Get messages from checkpoint
            if isinstance(checkpoint, dict):
                messages = checkpoint.get("channel_values", {}).get("messages", [])
            elif hasattr(checkpoint, 'channel_values'):
                messages = checkpoint.channel_values.get("messages", [])
            else:
                return

            # Check for incomplete tool calls
            tool_call_ids = set()
            tool_result_ids = set()

            # First pass: collect all tool call IDs and tool result IDs
            for msg in messages:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_call_ids.add(tool_call.get('id'))

                if hasattr(msg, 'tool_call_id'):
                    tool_result_ids.add(msg.tool_call_id)

            # Find tool calls without results
            incomplete_tool_calls = tool_call_ids - tool_result_ids

            if incomplete_tool_calls:
                logger.warning(f"Found {len(incomplete_tool_calls)} incomplete tool calls, cleaning history")
                # For now, we'll let LangGraph handle this naturally
                # The error suggests the issue is with the LLM provider validation
                # We could implement more sophisticated cleaning here if needed

        except Exception as e:
            logger.warning(f"Could not clean conversation history: {e}")

    def _get_tool_description(self, tool_name: str) -> str:
        """Get detailed description of what each tool does"""
        descriptions = {
            'search_products': 'Searched for courses, programs, and educational products',
            'search_information': 'Searched for general information and troubleshooting help',
            'handle_booking': 'Processed booking request and managed enrollment workflow'
        }
        return descriptions.get(tool_name, f'Used {tool_name}')

    def _add_booking_reminder_if_needed(self, response: str, user_message: str, thread_id: str) -> str:
        """Add booking reminder if user has pending booking and is asking about other topics"""
        # Only check for reminders if user is NOT currently talking about booking
        booking_keywords = ['book', 'booking', 'enroll', 'register', 'sign up', 'course enrollment']
        is_booking_related = any(keyword in user_message.lower() for keyword in booking_keywords)

        if not is_booking_related and self.current_user:
            try:
                # Get booking agent from ChatService (we need to access it through current_user)
                # This is a bit of a hack, but we need access to the booking agent
                from api.services.chat_service import ChatService
                chat_service = ChatService(self.current_user)
                booking_agent = chat_service.booking_agent

                reminder = booking_agent.get_pending_booking_reminder(thread_id)
                if reminder:
                    response += reminder
            except Exception as e:
                logger.warning(f"Could not check for pending bookings: {e}")

        return response

    def chat(self, message: str, thread_id: str = "default") -> dict:
        """
        Process a user message and return the response using LangGraph

        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management

        Returns:
            Dict with response and tools_used
        """
        if not self.agent:
            raise ValueError("Agent not initialized. Call set_tools() first.")

        # Validate message content
        if not message or not message.strip():
            return {
                "response": "I didn't receive any message. Please tell me how I can help you today!",
                "tools_used": []
            }

        # Limit message length to prevent API issues
        if len(message) > 2000:
            return {
                "response": "Your message is too long. Please keep it under 2000 characters and try again.",
                "tools_used": []
            }

        log_user_input(message)

        # Configure thread for memory persistence
        config = {"configurable": {"thread_id": thread_id}}

        try:
            # Debug: Check if there's existing conversation history
            try:
                existing_checkpoint = self.memory.get(config)
                if existing_checkpoint:
                    logger.info(f"🔍 Found existing conversation history for thread {thread_id}")
                    # Log the number of messages in history
                    if hasattr(existing_checkpoint, 'channel_values'):
                        messages = existing_checkpoint.channel_values.get("messages", [])
                        logger.info(f"📝 Previous conversation has {len(messages)} messages")
                    else:
                        logger.info(f"📝 Found checkpoint data: {type(existing_checkpoint)}")
                else:
                    logger.info(f"🆕 Starting new conversation for thread {thread_id}")
            except Exception as e:
                logger.warning(f"⚠️ Could not check conversation history: {e}")

            # Update user profile with conversation before processing
            if hasattr(self, 'memory_system') and self.memory_system:
                try:
                    # Get conversation messages for profile update
                    conversation_messages = self._get_conversation_messages(thread_id)
                    conversation_messages.append({"role": "user", "content": message})

                    # Update user profile
                    self.memory_system.update_student_profile(thread_id, conversation_messages)
                    logger.info("✅ Updated user profile with conversation")
                except Exception as e:
                    logger.warning(f"⚠️ Could not update user profile: {e}")

            # Invoke the agent with the message
            # The agent will use the existing conversation history from MongoDB
            try:
                response = self.agent.invoke(
                    {"messages": [HumanMessage(content=message)]},
                    config=config
                )
            except Exception as e:
                # Handle tool call history issues
                if "tool_calls that do not have a corresponding ToolMessage" in str(e):
                    logger.warning("⚠️ Tool call history issue detected, clearing conversation history")
                    # Clear the conversation history for this thread to start fresh
                    try:
                        # Clear from MongoDB memory
                        if hasattr(self.current_user, 'db'):
                            checkpoints_collection = self.current_user.db["checkpoints"]
                            checkpoint_writes_collection = self.current_user.db["checkpoint_writes"]

                            checkpoints_collection.delete_many({"thread_id": thread_id})
                            checkpoint_writes_collection.delete_many({"thread_id": thread_id})

                            logger.info("✅ Cleared conversation history, retrying...")

                            # Retry the request with clean history
                            response = self.agent.invoke(
                                {"messages": [HumanMessage(content=message)]},
                                config=config
                            )
                        else:
                            raise e
                    except Exception as retry_error:
                        logger.error(f"Failed to recover from tool call issue: {retry_error}")
                        raise e
                else:
                    raise e

            # Extract the final response
            final_response = response["messages"][-1].content

            # Check for pending bookings and add reminder if needed
            final_response = self._add_booking_reminder_if_needed(final_response, message, thread_id)

            log_agent_response(final_response)

            # Extract tool usage information from messages
            tools_used = []
            tool_results = {}

            # First pass: collect tool calls
            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_id = tool_call.get('id', tool_call['name'])
                        tools_used.append({
                            'id': tool_id,
                            'name': tool_call['name'],
                            'description': self._get_tool_description(tool_call['name']),
                            'input': tool_call.get('args', {}),
                            'output': ""  # Will be filled in next pass
                        })

            # Second pass: collect tool results
            for msg in response["messages"]:
                if hasattr(msg, 'tool_call_id') and hasattr(msg, 'content'):
                    tool_results[msg.tool_call_id] = msg.content

            # Match results to tool calls
            for tool in tools_used:
                if tool['id'] in tool_results:
                    tool['output'] = tool_results[tool['id']]

            return {
                "response": final_response,
                "tools_used": tools_used
            }
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)

            # Provide user-friendly error messages
            if "contents is not specified" in str(e) or "empty content" in str(e):
                user_response = "I didn't receive a valid message. Please tell me how I can help you today!"
            elif "400" in str(e) and "Gemini" in str(e):
                user_response = "I'm having trouble processing your request. Please try rephrasing your message."
            elif "timeout" in str(e).lower():
                user_response = "The request is taking too long. Please try again with a shorter message."
            else:
                user_response = "I'm experiencing technical difficulties. Please try again in a moment."

            return {
                "response": user_response,
                "tools_used": []
            }
