#!/usr/bin/env python3
"""
Test script to verify memory system fixes
"""

import os
import sys
import asyncio
import requests
import json
from datetime import datetime

# Add src to path
sys.path.append('src')

def test_memory_persistence():
    """Test memory persistence and information sharing"""
    
    # Test configuration
    BASE_URL = "http://localhost:8000/api/v1"
    
    # Test user credentials (you'll need to adjust these)
    test_user = {
        "username": "testuser",
        "password": "testpass"
    }
    
    print("🧪 Testing Memory System Fixes")
    print("=" * 50)
    
    try:
        # Step 1: Login to get token
        print("1. Logging in...")
        login_response = requests.post(
            f"{BASE_URL}/auth/login",
            data=test_user
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return False
            
        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        
        print("✅ Login successful")
        
        # Step 2: Clear any existing conversation
        print("\n2. Clearing existing conversation...")
        clear_response = requests.delete(
            f"{BASE_URL}/chat/clear",
            headers=headers
        )
        print(f"✅ Conversation cleared: {clear_response.status_code}")
        
        # Step 3: Start conversation with personal information
        print("\n3. Providing personal information...")
        personal_info_message = "Hi, my name is John Doe, my <NAME_EMAIL> and my phone number is 9841234567. I'm interested in learning about your courses."
        
        chat_response = requests.post(
            f"{BASE_URL}/chat/chat",
            json={"message": personal_info_message},
            headers=headers
        )
        
        if chat_response.status_code == 200:
            response_data = chat_response.json()
            print(f"✅ Personal info provided")
            print(f"Response: {response_data['response'][:100]}...")
        else:
            print(f"❌ Chat failed: {chat_response.status_code}")
            return False
        
        # Step 4: Ask about courses to trigger search
        print("\n4. Asking about courses...")
        course_message = "What courses do you have for programming?"
        
        chat_response = requests.post(
            f"{BASE_URL}/chat/chat",
            json={"message": course_message},
            headers=headers
        )
        
        if chat_response.status_code == 200:
            response_data = chat_response.json()
            print(f"✅ Course inquiry successful")
            print(f"Tools used: {[tool['name'] for tool in response_data.get('tools_used', [])]}")
        else:
            print(f"❌ Course inquiry failed: {chat_response.status_code}")
            return False
        
        # Step 5: Ask for personal information back
        print("\n5. Testing information disclosure...")
        info_request = "What's my name and contact information?"
        
        chat_response = requests.post(
            f"{BASE_URL}/chat/chat",
            json={"message": info_request},
            headers=headers
        )
        
        if chat_response.status_code == 200:
            response_data = chat_response.json()
            response_text = response_data['response']
            print(f"✅ Information request successful")
            print(f"Response: {response_text}")
            
            # Check if the response contains the expected information
            if "John Doe" in response_text and "<EMAIL>" in response_text:
                print("✅ Memory system is working - personal information retrieved!")
            elif "cannot disclose" in response_text.lower() or "personal information" in response_text.lower():
                print("❌ Information disclosure issue still exists")
                return False
            else:
                print("⚠️ Unclear response - check manually")
                
        else:
            print(f"❌ Information request failed: {chat_response.status_code}")
            return False
        
        # Step 6: Test persistence across new conversation
        print("\n6. Testing persistence...")
        persistence_message = "Do you remember my details?"
        
        chat_response = requests.post(
            f"{BASE_URL}/chat/chat",
            json={"message": persistence_message},
            headers=headers
        )
        
        if chat_response.status_code == 200:
            response_data = chat_response.json()
            response_text = response_data['response']
            print(f"✅ Persistence test successful")
            print(f"Response: {response_text}")
            
            if "John Doe" in response_text or "<EMAIL>" in response_text:
                print("✅ Memory persistence is working!")
            else:
                print("❌ Memory persistence issue")
                return False
                
        else:
            print(f"❌ Persistence test failed: {chat_response.status_code}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 All memory tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

if __name__ == "__main__":
    success = test_memory_persistence()
    sys.exit(0 if success else 1)
