#!/usr/bin/env python3
"""
Test the tool call history fix
"""

import os
import sys
from datetime import datetime

# Add src to path
sys.path.append('src')

def test_tool_call_fix():
    """Test that tool call history issues are handled properly"""
    
    print("🧪 Testing Tool Call History Fix")
    print("=" * 50)
    
    try:
        # Import required modules
        from api.services.agent_v2.main_agent import MainAgentV2
        from core.database import get_db_from_tenant_id
        from langchain_core.tools import tool
        
        print("✅ Modules imported successfully")
        
        # Create a mock user for testing
        tenant_id = "686a02179e8eee0d9f2c5db6"  # Ambition Guru tenant
        user_id = "tool_call_test_user"
        
        # Create mock user object
        class MockUser:
            def __init__(self, user_id, username):
                self.id = user_id
                self.username = username
                self.role = "agent"
        
        class MockCurrentUser:
            def __init__(self, tenant_id, user_id, username):
                self.tenant_id = tenant_id
                self.user = MockUser(user_id, username)
                self.db = get_db_from_tenant_id(tenant_id)
                self.vector_store_manager = None
        
        current_user = MockCurrentUser(tenant_id, user_id, "tool_test")
        
        # Clear any existing conversation history
        checkpoints_collection = current_user.db["checkpoints"]
        checkpoint_writes_collection = current_user.db["checkpoint_writes"]
        
        checkpoints_collection.delete_many({"thread_id": user_id})
        checkpoint_writes_collection.delete_many({"thread_id": user_id})
        
        print("✅ Cleared existing conversation history")
        
        # Initialize main agent
        main_agent = MainAgentV2(current_user=current_user)
        
        # Create tools that might cause issues
        @tool
        def search_information(user_message: str) -> str:
            """Search for information and troubleshooting help"""
            return f"Found information about: {user_message}"
        
        @tool
        def search_products(query: str) -> str:
            """Search for courses and products"""
            return f"Found products for: {query}"
        
        @tool
        def handle_booking(message: str, thread_id: str = "default") -> str:
            """Handle booking requests"""
            return f"Booking handled: {message}"
        
        # Set tools
        tools = [search_information, search_products, handle_booking]
        main_agent.set_tools(tools)
        
        print("✅ Main agent initialized with tools")
        
        # Test 1: Normal conversation that should trigger tool calls
        print("\n1. Testing normal tool call conversation...")
        message1 = "my app is not working"
        
        try:
            response1 = main_agent.chat(message1, user_id)
            print(f"✅ Response 1 successful: {len(response1['response'])} characters")
            print(f"Tools used: {[tool['name'] for tool in response1.get('tools_used', [])]}")
        except Exception as e:
            print(f"❌ Response 1 failed: {e}")
            return False
        
        # Test 2: Follow-up conversation that might trigger the tool call history issue
        print("\n2. Testing follow-up conversation...")
        message2 = "what courses do you have?"
        
        try:
            response2 = main_agent.chat(message2, user_id)
            print(f"✅ Response 2 successful: {len(response2['response'])} characters")
            print(f"Tools used: {[tool['name'] for tool in response2.get('tools_used', [])]}")
        except Exception as e:
            print(f"❌ Response 2 failed: {e}")
            return False
        
        # Test 3: Another follow-up to ensure stability
        print("\n3. Testing conversation stability...")
        message3 = "tell me about booking"
        
        try:
            response3 = main_agent.chat(message3, user_id)
            print(f"✅ Response 3 successful: {len(response3['response'])} characters")
            print(f"Tools used: {[tool['name'] for tool in response3.get('tools_used', [])]}")
        except Exception as e:
            print(f"❌ Response 3 failed: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 Tool call history fix test passed!")
        print("\nKey findings:")
        print("- Tool calls work correctly")
        print("- No tool call history errors")
        print("- Conversation flows smoothly")
        print("- Error handling works if issues occur")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tool_call_fix()
    sys.exit(0 if success else 1)
